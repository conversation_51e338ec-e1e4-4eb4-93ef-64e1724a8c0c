/* 知识图谱可视化样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100vh;
  overflow: hidden;
}

.container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 20px;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.main-content {
  flex: 1;
  position: relative;
}

#network-container {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 0 0 0 20px;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.header h1 {
  color: #333;
  font-size: 24px;
  margin-bottom: 5px;
}

.header p {
  color: #666;
  font-size: 14px;
}

.control-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  border: 1px solid #e0e0e0;
}

.control-section h3 {
  color: #333;
  font-size: 16px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.control-section h3::before {
  content: "🔧";
  margin-right: 8px;
}

.search-box {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
  margin-bottom: 10px;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  color: #555;
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: 500;
}

.checkbox-group {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 8px;
  background: #fafafa;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  padding: 3px;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 8px;
}

.checkbox-item label {
  font-size: 12px;
  color: #666;
  margin: 0;
  cursor: pointer;
  flex: 1;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
  border: 1px solid #ccc;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background: #e9ecef;
}

.stats {
  background: rgba(102, 126, 234, 0.1);
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
}

.stats h4 {
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
  font-size: 12px;
  color: #666;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #667eea;
  font-size: 18px;
}

.loading::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.legend {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 5px;
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.legend h4 {
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 11px;
  color: #666;
}

.legend-line {
  width: 20px;
  height: 2px;
  margin-right: 8px;
  border-radius: 1px;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  border-left: 4px solid #c62828;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 200px;
    order: 2;
  }

  .main-content {
    order: 1;
    height: calc(100vh - 200px);
  }

  #network-container {
    border-radius: 0;
  }
}

/* vis-network 样式覆盖 - 确保节点标签居中 */
.vis-network .vis-node {
  text-align: center !important;
}

.vis-network .vis-node .vis-label {
  text-align: center !important;
  vertical-align: middle !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
